@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 210 40% 98%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 210 100% 38%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;

    --font-sans:
      "SF Pro Display", system-ui, -apple-system, BlinkMacSystemFont,
      "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    --font-display:
      "SF Pro Display", system-ui, -apple-system, BlinkMacSystemFont,
      "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  /* Dark Modern Legal Theme */
  .legal-dark {
    --background: 232 21% 14%; /* #1B1D2A */
    --foreground: 240 20% 95%; /* #F2F2F5 */

    --card: 232 21% 14%; /* #1B1D2A */
    --card-foreground: 240 20% 95%;

    --popover: 232 21% 14%;
    --popover-foreground: 240 20% 95%;

    --primary: 335 74% 57%; /* #E1467C */
    --primary-foreground: 240 20% 95%;

    --secondary: 228 13% 21%; /* #2E2F3A */
    --secondary-foreground: 240 20% 95%;

    --muted: 228 13% 21%;
    --muted-foreground: 240 20% 75%;

    --accent: 276 72% 60%; /* #9B51E0 */
    --accent-foreground: 240 20% 95%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 240 20% 95%;

    --border: 228 13% 21%;
    --input: 228 13% 21%;
    --ring: 335 74% 57%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
    font-feature-settings:
      "ss01" on,
      "ss02" on,
      "ss03" on,
      "ss04" on;
  }

  /* SF Pro Display Font */
  @font-face {
    font-family: "SF Pro Display";
    src: url("https://applesocial.s3.amazonaws.com/assets/styles/fonts/sanfrancisco/sanfranciscodisplay-regular-webfont.woff2")
      format("woff2");
    font-weight: 400;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: "SF Pro Display";
    src: url("https://applesocial.s3.amazonaws.com/assets/styles/fonts/sanfrancisco/sanfranciscodisplay-medium-webfont.woff2")
      format("woff2");
    font-weight: 500;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: "SF Pro Display";
    src: url("https://applesocial.s3.amazonaws.com/assets/styles/fonts/sanfrancisco/sanfranciscodisplay-semibold-webfont.woff2")
      format("woff2");
    font-weight: 600;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: "SF Pro Display";
    src: url("https://applesocial.s3.amazonaws.com/assets/styles/fonts/sanfrancisco/sanfranciscodisplay-bold-webfont.woff2")
      format("woff2");
    font-weight: 700;
    font-style: normal;
    font-display: swap;
  }
}

@layer components {
  .glass-card {
    @apply bg-white/80 backdrop-blur-lg border border-white/20 shadow-sm;
  }

  .glass-card-dark {
    @apply bg-gray-900/80 backdrop-blur-lg border border-gray-800/30 shadow-md;
  }

  .input-focus-ring {
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2;
  }

  .text-balance {
    text-wrap: balance;
  }

  /* Legal dark theme specific styles */
  .legal-gradient-text {
    @apply bg-gradient-to-r from-darkLegal-accent1 to-darkLegal-accent2 bg-clip-text text-transparent;
  }

  .legal-gradient-bg {
    @apply bg-gradient-to-r from-darkLegal-accent1 to-darkLegal-accent2;
  }

  .legal-card {
    @apply bg-darkLegal-gray border border-darkLegal-gray/50 rounded-md p-4;
  }
}

/* Loading indicator */
.loader {
  @apply relative w-10 h-10;
}

.loader:before,
.loader:after {
  content: "";
  @apply absolute top-0 left-0 w-full h-full rounded-full;
  animation: pulse 2s linear infinite;
}

.loader:after {
  animation-delay: 1s;
}

@keyframes pulse {
  0%,
  100% {
    @apply transform scale-0 opacity-100 bg-legal-500;
  }
  50% {
    /* Fixed the scale-1 error by using scale-100 which is valid in Tailwind */
    @apply transform scale-100 opacity-0 bg-legal-400;
  }
}

/* Page transitions */
.page-transition-enter {
  opacity: 0;
  transform: scale(0.98);
}

.page-transition-enter-active {
  opacity: 1;
  transform: scale(1);
  transition:
    opacity 300ms,
    transform 300ms;
}

.page-transition-exit {
  opacity: 1;
  transform: scale(1);
}

.page-transition-exit-active {
  opacity: 0;
  transform: scale(0.98);
  transition:
    opacity 300ms,
    transform 300ms;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-transparent;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-300 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400;
}

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
}

/* TipTap Editor A4 Document Styling */
.rm-with-pagination {
  /* Document container styling */
  background: #f8fafc !important;
  min-height: 100vh;
  padding: 2rem 1rem;
}

.rm-with-pagination .ProseMirror {
  /* Main editor content area */
  background: #ffffff;
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  min-height: 297mm; /* A4 height */
  width: 210mm; /* A4 width */
  margin: 0 auto;
  position: relative;
  font-family: "Times New Roman", serif;
  font-size: 11pt;
  line-height: 1.6;
  color: #1a202c;
}

.rm-with-pagination .rm-pagination-gap {
  /* Page break styling */
  background: #e2e8f0 !important;
  border-left: 1px solid #cbd5e1 !important;
  border-right: 1px solid #cbd5e1 !important;
  position: relative;
}

/* Enhanced page styling */
.rm-with-pagination .page {
  background: #ffffff;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Print-friendly styling */
@media print {
  .rm-with-pagination {
    background: #ffffff !important;
    padding: 0 !important;
    box-shadow: none !important;
  }

  .rm-with-pagination .ProseMirror {
    box-shadow: none !important;
    border: none !important;
    border-radius: 0 !important;
    margin: 0 !important;
    width: 100% !important;
    max-width: none !important;
  }

  .rm-with-pagination .rm-pagination-gap {
    display: none !important;
  }
}

/* Responsive design for smaller screens */
@media (max-width: 768px) {
  .rm-with-pagination {
    padding: 1rem 0.5rem;
  }

  .rm-with-pagination .ProseMirror {
    width: 100%;
    max-width: calc(100vw - 2rem);
    min-height: auto;
  }
}

/* Change Tracking Styles */
.ProseMirror insert,
.ProseMirror [data-mark-type="insertion"] {
  background-color: #dcfce7; /* Light green background */
  color: #166534; /* Dark green text */
  text-decoration: none;
  border-radius: 2px;
  padding: 1px 2px;
}

.ProseMirror delete,
.ProseMirror [data-mark-type="deletion"] {
  background-color: #fef2f2; /* Light red background */
  color: #dc2626; /* Red text */
  text-decoration: line-through;
  border-radius: 2px;
  padding: 1px 2px;
}

/* Change tracking hover effects */
.ProseMirror insert:hover,
.ProseMirror [data-mark-type="insertion"]:hover {
  background-color: #bbf7d0; /* Darker green on hover */
}

.ProseMirror delete:hover,
.ProseMirror [data-mark-type="deletion"]:hover {
  background-color: #fecaca; /* Darker red on hover */
}

/* History panel styles */
.history-panel {
  @apply bg-white border border-gray-200 rounded-lg shadow-lg p-4 max-h-96 overflow-y-auto;
}

.history-item {
  @apply p-3 border-b border-gray-100 last:border-b-0 hover:bg-gray-50 cursor-pointer transition-colors;
}

.history-item:hover {
  @apply bg-gray-50;
}

.history-time {
  @apply text-xs text-gray-500 font-mono;
}

.history-message {
  @apply text-sm font-medium text-gray-900 mt-1;
}

.history-stats {
  @apply text-xs text-gray-600 mt-1;
}

/* Save button styles */
.save-button {
  @apply bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md font-medium transition-colors;
}

.save-button:disabled {
  @apply bg-gray-400 cursor-not-allowed;
}

/* Blame/highlight marker styles */
.blame-marker {
  background-color: #fef3c7; /* Yellow highlight */
  border-radius: 2px;
  padding: 1px 2px;
}

/* Dark mode support for change tracking */
.dark .ProseMirror insert,
.dark .ProseMirror [data-mark-type="insertion"] {
  background-color: #14532d; /* Dark green background */
  color: #86efac; /* Light green text */
}

.dark .ProseMirror delete,
.dark .ProseMirror [data-mark-type="deletion"] {
  background-color: #7f1d1d; /* Dark red background */
  color: #fca5a5; /* Light red text */
}

.dark .history-panel {
  @apply bg-gray-800 border-gray-700;
}

.dark .history-item {
  @apply border-gray-700 hover:bg-gray-700;
}

.dark .history-item:hover {
  @apply bg-gray-700;
}

.dark .history-message {
  @apply text-gray-100;
}

.dark .blame-marker {
  background-color: #92400e; /* Dark yellow highlight */
}
