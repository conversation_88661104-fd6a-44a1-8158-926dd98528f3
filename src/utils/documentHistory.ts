import { Step } from "@tiptap/pm/transform";
import { Mapping } from "@tiptap/pm/transform";

export interface DocumentCommit {
  id: string;
  message: string;
  timestamp: Date;
  content: string;
  steps: any[]; // Serialized steps
  maps: any[]; // Serialized maps
  author?: string;
  hidden?: boolean;
}

export interface DocumentHistoryState {
  commits: DocumentCommit[];
  uncommittedSteps: any[];
  uncommittedMaps: any[];
  currentContent: string;
  lastSavedContent: string;
}

export interface BlameSpan {
  from: number;
  to: number;
  commitId: string | null;
}

export class DocumentHistoryManager {
  private documentId: string;
  private storageKey: string;

  constructor(documentId: string) {
    this.documentId = documentId;
    this.storageKey = `document_history_${documentId}`;
  }

  /**
   * Initialize or load existing document history
   */
  initializeHistory(initialContent: string = ""): DocumentHistoryState {
    const stored = this.loadFromStorage();
    if (stored) {
      return stored;
    }

    // Create initial state
    const initialState: DocumentHistoryState = {
      commits: [],
      uncommittedSteps: [],
      uncommittedMaps: [],
      currentContent: initialContent,
      lastSavedContent: initialContent,
    };

    this.saveToStorage(initialState);
    return initialState;
  }

  /**
   * Save current state to localStorage
   */
  saveToStorage(state: DocumentHistoryState): void {
    try {
      const serialized = {
        ...state,
        commits: state.commits.map(commit => ({
          ...commit,
          timestamp: commit.timestamp.toISOString(),
        })),
      };
      localStorage.setItem(this.storageKey, JSON.stringify(serialized));
    } catch (error) {
      console.error("Failed to save document history:", error);
    }
  }

  /**
   * Load state from localStorage
   */
  loadFromStorage(): DocumentHistoryState | null {
    try {
      const stored = localStorage.getItem(this.storageKey);
      if (!stored) return null;

      const parsed = JSON.parse(stored);
      return {
        ...parsed,
        commits: parsed.commits.map((commit: any) => ({
          ...commit,
          timestamp: new Date(commit.timestamp),
        })),
      };
    } catch (error) {
      console.error("Failed to load document history:", error);
      return null;
    }
  }

  /**
   * Create a new commit with the current changes
   */
  createCommit(
    state: DocumentHistoryState,
    message: string,
    content: string,
    author?: string
  ): DocumentHistoryState {
    if (state.uncommittedSteps.length === 0 && content === state.lastSavedContent) {
      return state; // No changes to commit
    }

    const commit: DocumentCommit = {
      id: this.generateCommitId(),
      message,
      timestamp: new Date(),
      content,
      steps: state.uncommittedSteps,
      maps: state.uncommittedMaps,
      author,
    };

    const newState: DocumentHistoryState = {
      commits: [...state.commits, commit],
      uncommittedSteps: [],
      uncommittedMaps: [],
      currentContent: content,
      lastSavedContent: content,
    };

    this.saveToStorage(newState);
    return newState;
  }

  /**
   * Add uncommitted changes to the history
   */
  addUncommittedChanges(
    state: DocumentHistoryState,
    steps: any[],
    maps: any[],
    currentContent: string
  ): DocumentHistoryState {
    const newState: DocumentHistoryState = {
      ...state,
      uncommittedSteps: [...state.uncommittedSteps, ...steps],
      uncommittedMaps: [...state.uncommittedMaps, ...maps],
      currentContent,
    };

    this.saveToStorage(newState);
    return newState;
  }

  /**
   * Get all commits for display
   */
  getCommits(state: DocumentHistoryState): DocumentCommit[] {
    return state.commits.filter(commit => !commit.hidden);
  }

  /**
   * Check if there are uncommitted changes
   */
  hasUncommittedChanges(state: DocumentHistoryState): boolean {
    return state.uncommittedSteps.length > 0 || 
           state.currentContent !== state.lastSavedContent;
  }

  /**
   * Get the difference between current content and last saved content
   */
  getCurrentDiff(state: DocumentHistoryState): {
    hasChanges: boolean;
    addedChars: number;
    removedChars: number;
  } {
    const current = state.currentContent;
    const saved = state.lastSavedContent;
    
    if (current === saved) {
      return { hasChanges: false, addedChars: 0, removedChars: 0 };
    }

    // Simple character-based diff calculation
    const addedChars = Math.max(0, current.length - saved.length);
    const removedChars = Math.max(0, saved.length - current.length);

    return {
      hasChanges: true,
      addedChars,
      removedChars,
    };
  }

  /**
   * Revert to a specific commit
   */
  revertToCommit(
    state: DocumentHistoryState,
    commitId: string
  ): { success: boolean; content?: string; error?: string } {
    const commit = state.commits.find(c => c.id === commitId);
    if (!commit) {
      return { success: false, error: "Commit not found" };
    }

    if (this.hasUncommittedChanges(state)) {
      return { 
        success: false, 
        error: "Cannot revert with uncommitted changes. Please save or discard changes first." 
      };
    }

    return { success: true, content: commit.content };
  }

  /**
   * Clear all history (useful for testing or reset)
   */
  clearHistory(): void {
    localStorage.removeItem(this.storageKey);
  }

  /**
   * Generate a unique commit ID
   */
  private generateCommitId(): string {
    return `commit_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Export history as JSON (for backup/sharing)
   */
  exportHistory(state: DocumentHistoryState): string {
    return JSON.stringify({
      ...state,
      commits: state.commits.map(commit => ({
        ...commit,
        timestamp: commit.timestamp.toISOString(),
      })),
      exportedAt: new Date().toISOString(),
      documentId: this.documentId,
    }, null, 2);
  }

  /**
   * Import history from JSON
   */
  importHistory(jsonData: string): DocumentHistoryState | null {
    try {
      const data = JSON.parse(jsonData);
      const state: DocumentHistoryState = {
        ...data,
        commits: data.commits.map((commit: any) => ({
          ...commit,
          timestamp: new Date(commit.timestamp),
        })),
      };
      this.saveToStorage(state);
      return state;
    } catch (error) {
      console.error("Failed to import history:", error);
      return null;
    }
  }
}

/**
 * Create a global instance for easy access
 */
export const createDocumentHistoryManager = (documentId: string) => {
  return new DocumentHistoryManager(documentId);
};
