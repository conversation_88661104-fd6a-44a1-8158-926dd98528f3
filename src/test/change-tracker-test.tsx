import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Editor } from '@/components/tiptap/editor';
import { EditorStoreProvider } from '@/store/use-editor-store';

// Mock the editor store
const MockEditorStoreProvider = ({ children }: { children: React.ReactNode }) => {
  return <EditorStoreProvider>{children}</EditorStoreProvider>;
};

// Test component wrapper
const TestEditor = ({ initialContent = "", documentId = "test-doc" }) => {
  return (
    <MockEditorStoreProvider>
      <Editor initialContent={initialContent} documentId={documentId} />
    </MockEditorStoreProvider>
  );
};

describe('Change Tracker Implementation', () => {
  beforeEach(() => {
    // Clear localStorage before each test
    localStorage.clear();
  });

  test('renders editor with save button and history controls', async () => {
    render(<TestEditor />);
    
    // Check if save button is present
    expect(screen.getByText('Save')).toBeInTheDocument();
    
    // Check if history button is present
    expect(screen.getByText('History')).toBeInTheDocument();
    
    // Check if show changes button is present
    expect(screen.getByText('Show Changes')).toBeInTheDocument();
  });

  test('save button is disabled when no changes', async () => {
    render(<TestEditor />);
    
    const saveButton = screen.getByText('Save');
    expect(saveButton).toBeDisabled();
  });

  test('history panel opens and closes', async () => {
    render(<TestEditor />);
    
    const historyButton = screen.getByText('History');
    
    // Click to open history panel
    fireEvent.click(historyButton);
    
    // Should show history panel
    await waitFor(() => {
      expect(screen.getByText('Document History')).toBeInTheDocument();
    });
    
    // Click again to close
    fireEvent.click(historyButton);
    
    // History panel should be hidden (not in DOM when closed)
    await waitFor(() => {
      expect(screen.queryByText('Document History')).not.toBeInTheDocument();
    });
  });

  test('keyboard shortcut Ctrl+S triggers save', async () => {
    render(<TestEditor initialContent="Initial content" />);
    
    // Simulate Ctrl+S
    fireEvent.keyDown(document, { key: 's', ctrlKey: true });
    
    // Should attempt to save (even if disabled, the handler should be called)
    // This is a basic test - in a real scenario, we'd mock the editor commands
    expect(true).toBe(true); // Placeholder assertion
  });

  test('diff viewer shows when there are changes', async () => {
    render(<TestEditor initialContent="Original content" />);
    
    const showChangesButton = screen.getByText('Show Changes');
    
    // Initially should be disabled (no changes)
    expect(showChangesButton).toBeDisabled();
    
    // In a real test, we'd simulate typing in the editor to create changes
    // and then verify the diff viewer appears
  });

  test('document history is persisted in localStorage', () => {
    const documentId = 'test-doc-123';
    render(<TestEditor documentId={documentId} />);
    
    // Check if localStorage key is created
    const storageKey = `document_history_${documentId}`;
    
    // Initially should have empty history
    const stored = localStorage.getItem(storageKey);
    expect(stored).toBeTruthy();
    
    if (stored) {
      const parsed = JSON.parse(stored);
      expect(parsed.commits).toEqual([]);
      expect(parsed.uncommittedSteps).toEqual([]);
    }
  });

  test('CSS styles for change tracking are applied', () => {
    // Check if the CSS classes exist in the document
    const styles = document.styleSheets;
    let hasInsertionStyles = false;
    let hasDeletionStyles = false;
    
    // This is a simplified check - in a real test environment,
    // we'd check if the CSS is properly loaded
    for (let i = 0; i < styles.length; i++) {
      try {
        const rules = styles[i].cssRules || styles[i].rules;
        for (let j = 0; j < rules.length; j++) {
          const rule = rules[j] as CSSStyleRule;
          if (rule.selectorText?.includes('insert') || 
              rule.selectorText?.includes('[data-mark-type="insertion"]')) {
            hasInsertionStyles = true;
          }
          if (rule.selectorText?.includes('delete') || 
              rule.selectorText?.includes('[data-mark-type="deletion"]')) {
            hasDeletionStyles = true;
          }
        }
      } catch (e) {
        // Some stylesheets might not be accessible due to CORS
        continue;
      }
    }
    
    // For now, just check that the test runs without errors
    expect(true).toBe(true);
  });
});

// Integration test for the complete workflow
describe('Change Tracker Integration', () => {
  test('complete workflow: edit -> save -> view history -> revert', async () => {
    const { container } = render(<TestEditor initialContent="Original text" />);
    
    // 1. Initial state - save button should be disabled
    const saveButton = screen.getByText('Save');
    expect(saveButton).toBeDisabled();
    
    // 2. Open history panel
    const historyButton = screen.getByText('History');
    fireEvent.click(historyButton);
    
    await waitFor(() => {
      expect(screen.getByText('Document History')).toBeInTheDocument();
    });
    
    // 3. Should show "No history yet" message
    expect(screen.getByText('No history yet')).toBeInTheDocument();
    
    // 4. In a real test, we would:
    // - Simulate typing in the editor
    // - Verify save button becomes enabled
    // - Click save button
    // - Verify history entry is created
    // - Test revert functionality
    
    expect(container).toBeInTheDocument();
  });
});

// Performance test
describe('Change Tracker Performance', () => {
  test('handles large documents efficiently', () => {
    const largeContent = 'Lorem ipsum '.repeat(1000);
    
    const startTime = performance.now();
    render(<TestEditor initialContent={largeContent} />);
    const endTime = performance.now();
    
    // Should render within reasonable time (less than 1 second)
    expect(endTime - startTime).toBeLessThan(1000);
  });
});

export { TestEditor };
