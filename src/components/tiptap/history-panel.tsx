import React, { useCallback, useEffect, useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { 
  History, 
  Clock, 
  User, 
  RotateCcw, 
  ChevronDown, 
  ChevronRight,
  FileText,
  Plus,
  Minus
} from 'lucide-react';
import { useEditorStore } from '@/store/use-editor-store';
import { DocumentCommit, DocumentHistoryState } from '@/utils/documentHistory';
import { cn } from '@/lib/utils';

interface HistoryPanelProps {
  className?: string;
  isOpen?: boolean;
  onToggle?: () => void;
}

export const HistoryPanel: React.FC<HistoryPanelProps> = ({ 
  className, 
  isOpen = false,
  onToggle 
}) => {
  const { editor } = useEditorStore();
  const [historyState, setHistoryState] = useState<DocumentHistoryState | null>(null);
  const [selectedCommit, setSelectedCommit] = useState<string | null>(null);
  const [isExpanded, setIsExpanded] = useState(isOpen);

  // Fetch history state
  const fetchHistoryState = useCallback(() => {
    if (!editor) return;
    
    try {
      editor.commands.getDocumentHistory();
      const history = editor.extensionManager.extensions
        .find(ext => ext.name === 'trackchange')?.storage?.documentHistory;
      setHistoryState(history || null);
    } catch (error) {
      console.error('Error fetching document history:', error);
    }
  }, [editor]);

  // Revert to a specific commit
  const handleRevertToCommit = useCallback(async (commit: DocumentCommit) => {
    if (!editor || !historyState) return;

    // Check for uncommitted changes
    editor.commands.hasUncommittedChanges();
    const hasChanges = editor.extensionManager.extensions
      .find(ext => ext.name === 'trackchange')?.storage?.hasUncommittedChanges || false;

    if (hasChanges) {
      const confirmed = window.confirm(
        'You have uncommitted changes. Reverting will lose these changes. Continue?'
      );
      if (!confirmed) return;
    }

    try {
      // Set the editor content to the commit's content
      editor.commands.setContent(commit.content);
      
      // Create a new commit for the revert
      const revertMessage = `Revert to: ${commit.message}`;
      editor.commands.saveDocument(revertMessage);
      
      // Refresh history
      fetchHistoryState();
    } catch (error) {
      console.error('Error reverting to commit:', error);
    }
  }, [editor, historyState, fetchHistoryState]);

  // Format timestamp
  const formatTimestamp = (date: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours}h ago`;
    
    const diffDays = Math.floor(diffHours / 24);
    if (diffDays < 7) return `${diffDays}d ago`;
    
    return date.toLocaleDateString();
  };

  // Get commit stats (simplified)
  const getCommitStats = (commit: DocumentCommit) => {
    const steps = commit.steps.length;
    return {
      changes: steps,
      additions: Math.floor(steps * 0.6), // Rough estimate
      deletions: Math.floor(steps * 0.4), // Rough estimate
    };
  };

  // Update history when editor changes
  useEffect(() => {
    if (!editor) return;

    const handleUpdate = () => {
      fetchHistoryState();
    };

    editor.on('update', handleUpdate);
    editor.on('transaction', handleUpdate);

    // Initial fetch
    fetchHistoryState();

    return () => {
      editor.off('update', handleUpdate);
      editor.off('transaction', handleUpdate);
    };
  }, [editor, fetchHistoryState]);

  // Sync isExpanded with isOpen prop
  useEffect(() => {
    setIsExpanded(isOpen);
  }, [isOpen]);

  const commits = historyState?.commits || [];
  const hasUncommittedChanges = historyState ? 
    historyState.currentContent !== historyState.lastSavedContent ||
    historyState.uncommittedSteps.length > 0 : false;

  return (
    <div className={cn("history-panel border-l bg-white", className)}>
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b bg-gray-50">
        <div className="flex items-center gap-2">
          <History className="h-4 w-4 text-gray-600" />
          <span className="font-medium text-sm">Document History</span>
          {hasUncommittedChanges && (
            <div className="h-2 w-2 bg-amber-500 rounded-full animate-pulse" />
          )}
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => {
            setIsExpanded(!isExpanded);
            onToggle?.();
          }}
          className="h-6 w-6 p-0"
        >
          {isExpanded ? (
            <ChevronDown className="h-3 w-3" />
          ) : (
            <ChevronRight className="h-3 w-3" />
          )}
        </Button>
      </div>

      {/* Content */}
      {isExpanded && (
        <div className="flex flex-col h-full">
          {/* Current changes indicator */}
          {hasUncommittedChanges && (
            <div className="p-3 bg-amber-50 border-b border-amber-200">
              <div className="flex items-center gap-2 text-sm text-amber-800">
                <FileText className="h-4 w-4" />
                <span className="font-medium">Unsaved Changes</span>
              </div>
              <p className="text-xs text-amber-600 mt-1">
                You have uncommitted changes. Save to create a new version.
              </p>
            </div>
          )}

          {/* History list */}
          <ScrollArea className="flex-1">
            <div className="p-2">
              {commits.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <History className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">No history yet</p>
                  <p className="text-xs">Save your document to create the first version</p>
                </div>
              ) : (
                <div className="space-y-2">
                  {commits.slice().reverse().map((commit, index) => {
                    const stats = getCommitStats(commit);
                    const isSelected = selectedCommit === commit.id;
                    const isLatest = index === 0;

                    return (
                      <div
                        key={commit.id}
                        className={cn(
                          "history-item rounded-md border p-3 cursor-pointer transition-all",
                          isSelected ? "border-blue-300 bg-blue-50" : "border-gray-200",
                          isLatest && "border-green-300 bg-green-50"
                        )}
                        onClick={() => setSelectedCommit(isSelected ? null : commit.id)}
                      >
                        {/* Commit header */}
                        <div className="flex items-start justify-between">
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2">
                              <div className="history-time text-xs font-mono bg-gray-100 px-2 py-1 rounded">
                                {formatTimestamp(commit.timestamp)}
                              </div>
                              {isLatest && (
                                <span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">
                                  Latest
                                </span>
                              )}
                            </div>
                            <div className="history-message mt-1 font-medium text-sm">
                              {commit.message}
                            </div>
                            {commit.author && (
                              <div className="flex items-center gap-1 mt-1 text-xs text-gray-600">
                                <User className="h-3 w-3" />
                                <span>{commit.author}</span>
                              </div>
                            )}
                          </div>
                          {!isLatest && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleRevertToCommit(commit);
                              }}
                              className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100"
                            >
                              <RotateCcw className="h-3 w-3" />
                            </Button>
                          )}
                        </div>

                        {/* Commit stats */}
                        <div className="history-stats flex items-center gap-3 mt-2 text-xs text-gray-600">
                          <div className="flex items-center gap-1">
                            <FileText className="h-3 w-3" />
                            <span>{stats.changes} changes</span>
                          </div>
                          {stats.additions > 0 && (
                            <div className="flex items-center gap-1 text-green-600">
                              <Plus className="h-3 w-3" />
                              <span>{stats.additions}</span>
                            </div>
                          )}
                          {stats.deletions > 0 && (
                            <div className="flex items-center gap-1 text-red-600">
                              <Minus className="h-3 w-3" />
                              <span>{stats.deletions}</span>
                            </div>
                          )}
                        </div>

                        {/* Expanded details */}
                        {isSelected && (
                          <div className="mt-3 pt-3 border-t border-gray-200">
                            <div className="text-xs text-gray-600">
                              <div className="flex items-center gap-2 mb-2">
                                <Clock className="h-3 w-3" />
                                <span>{commit.timestamp.toLocaleString()}</span>
                              </div>
                              <div className="bg-gray-50 p-2 rounded text-xs font-mono">
                                ID: {commit.id}
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          </ScrollArea>
        </div>
      )}
    </div>
  );
};
