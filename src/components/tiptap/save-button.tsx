import React, { useCallback, useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import { Save, Clock } from "lucide-react";
import { useEditorStore } from "@/store/use-editor-store";
import { cn } from "@/lib/utils";

interface SaveButtonProps {
  className?: string;
  showLastSaved?: boolean;
}

export const SaveButton: React.FC<SaveButtonProps> = ({
  className,
  showLastSaved = true,
}) => {
  const { editor } = useEditorStore();
  const [isSaving, setIsSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Check for unsaved changes
  const checkUnsavedChanges = useCallback(() => {
    if (!editor) return;

    try {
      editor.commands.hasUncommittedChanges();
      const hasChanges =
        editor.extensionManager.extensions.find(
          (ext) => ext.name === "trackchange"
        )?.storage?.hasUncommittedChanges || false;
      setHasUnsavedChanges(hasChanges);
    } catch (error) {
      console.error("Error checking unsaved changes:", error);
    }
  }, [editor]);

  // Save document
  const handleSave = useCallback(async () => {
    if (!editor || isSaving) return;

    setIsSaving(true);
    try {
      const success = editor.commands.saveDocument();
      if (success) {
        setLastSaved(new Date());
        setHasUnsavedChanges(false);
      }
    } catch (error) {
      console.error("Error saving document:", error);
    } finally {
      setIsSaving(false);
    }
  }, [editor, isSaving]);

  // Keyboard shortcut handler
  const handleKeyDown = useCallback(
    (event: KeyboardEvent) => {
      if ((event.ctrlKey || event.metaKey) && event.key === "s") {
        event.preventDefault();
        handleSave();
      }
    },
    [handleSave]
  );

  // Set up keyboard shortcut
  useEffect(() => {
    document.addEventListener("keydown", handleKeyDown);
    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [handleKeyDown]);

  // Check for changes on editor updates
  useEffect(() => {
    if (!editor) return;

    const handleUpdate = () => {
      checkUnsavedChanges();
    };

    editor.on("update", handleUpdate);
    editor.on("transaction", handleUpdate);

    // Initial check
    checkUnsavedChanges();

    return () => {
      editor.off("update", handleUpdate);
      editor.off("transaction", handleUpdate);
    };
  }, [editor, checkUnsavedChanges]);

  const formatLastSaved = (date: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);

    if (diffMins < 1) return "Just now";
    if (diffMins < 60) return `${diffMins}m ago`;

    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours}h ago`;

    return date.toLocaleDateString();
  };

  return (
    <div className={cn("flex items-center gap-2", className)}>
      <Button
        onClick={handleSave}
        disabled={!editor || isSaving || !hasUnsavedChanges}
        className={cn(
          "save-button flex items-center gap-2",
          hasUnsavedChanges ? "bg-green-600 hover:bg-green-700" : "bg-gray-400"
        )}
        size="sm"
      >
        <Save className={cn("h-4 w-4", isSaving && "animate-spin")} />
        {isSaving ? "Saving..." : "Save"}
        {hasUnsavedChanges && (
          <span className="text-xs bg-white/20 px-1 rounded">Ctrl+S</span>
        )}
      </Button>

      {showLastSaved && lastSaved && (
        <div className="flex items-center gap-1 text-xs text-gray-500">
          <Clock className="h-3 w-3" />
          <span>Saved {formatLastSaved(lastSaved)}</span>
        </div>
      )}

      {hasUnsavedChanges && (
        <div className="flex items-center gap-1 text-xs text-amber-600">
          <div className="h-2 w-2 bg-amber-500 rounded-full animate-pulse" />
          <span>Unsaved changes</span>
        </div>
      )}
    </div>
  );
};

// Hook for external components to trigger save
export const useSaveDocument = () => {
  const { editor } = useEditorStore();

  const saveDocument = useCallback(
    async (message?: string) => {
      if (!editor) return false;

      try {
        return editor.commands.saveDocument(message);
      } catch (error) {
        console.error("Error saving document:", error);
        return false;
      }
    },
    [editor]
  );

  const hasUnsavedChanges = useCallback(() => {
    if (!editor) return false;

    try {
      editor.commands.hasUncommittedChanges();
      return (
        editor.extensionManager.extensions.find(
          (ext) => ext.name === "trackchange"
        )?.storage?.hasUncommittedChanges || false
      );
    } catch (error) {
      console.error("Error checking unsaved changes:", error);
      return false;
    }
  }, [editor]);

  return {
    saveDocument,
    hasUnsavedChanges,
  };
};
