"use client";

import { Editor<PERSON>ontent, useEditor } from "@tiptap/react";
import { StarterKit } from "@tiptap/starter-kit";
import { TrackChangeExtension } from "./extensions/change-tracker";
import { TaskItem } from "@tiptap/extension-task-item";
import { TaskList } from "@tiptap/extension-task-list";

import {
  PaginationPlus,
  TableCellPlus,
  TableHeaderPlus,
  TablePlus,
  TableRowPlus,
  type MarginConfig,
  type PaperSize,
} from "./extensions/pagination";

import { Image } from "@tiptap/extension-image";

import { FontFamily } from "@tiptap/extension-font-family";
import { TextStyle } from "@tiptap/extension-text-style";
import { Underline } from "@tiptap/extension-underline";

import { Color } from "@tiptap/extension-color";
import { Highlight } from "@tiptap/extension-highlight";

import { TextAlign } from "@tiptap/extension-text-align";

import CommentExtension from "@sereneinserenade/tiptap-comment-extension";
import { Link } from "@tiptap/extension-link";
import { Placeholder } from "@tiptap/extension-placeholder";

import { FontSizeExtensions } from "@/extensions/font-size";
import { LineHeightExtension } from "@/extensions/line-height";
import { useEditorStore } from "@/store/use-editor-store";
import { useEditorComments } from "@/contexts/EditorCommentsContext";
import { useCallback, useEffect, useRef, useState } from "react";
import { AlignmentHoverMenu } from "./alignment-hover-menu";
import { CommentDialog } from "./comment-dialog";
import { SaveButton } from "./save-button";
import { HistoryPanel } from "./history-panel";
import { DiffViewer, useDocumentDiff } from "./diff-viewer";

interface EditorProps {
  initialContent?: string | undefined;
  documentId: string;
}

export const Editor = ({ initialContent, documentId }: EditorProps) => {
  const [isCommentDialogOpen, setIsCommentDialogOpen] = useState(false);
  const [selectedText, setSelectedText] = useState("");
  const [isHistoryPanelOpen, setIsHistoryPanelOpen] = useState(false);
  const [showDiffViewer, setShowDiffViewer] = useState(false);

  const { setEditor, setActiveCommentId } = useEditorStore();

  // Get editor comments context (always call hook)
  const editorCommentsContext = useEditorComments();

  const editorRef = useRef<HTMLDivElement>(null);
  const focusCommentWithActiveId = (commentId: string) => {
    // This will be used to scroll to and highlight the comment in the panel
    const commentElement = document.querySelector(
      `[data-comment-id="${commentId}"]`
    );
    if (commentElement) {
      commentElement.scrollIntoView({ behavior: "smooth", block: "center" });
    }
  };

  const handleCommentSubmit = async (
    commentId: string,
    content: string,
    author: string
  ) => {
    if (!editor) return;

    console.log("Applying comment with ID:", commentId);

    // Apply comment to selected text in editor with server-generated ID
    editor.chain().focus().setComment(commentId).run();

    // Check if the comment was applied
    setTimeout(() => {
      const commentElements = document.querySelectorAll(
        `[data-comment-id="${commentId}"]`
      );
      console.log("Comment elements after applying:", commentElements);

      // Also check all comment-highlight elements to see what attributes they have
      const allCommentElements =
        document.querySelectorAll(".comment-highlight");
      console.log("All comment elements after applying:", allCommentElements);
      allCommentElements.forEach((el, index) => {
        console.log(`Element ${index}:`, el.outerHTML);
      });
    }, 100);

    setActiveCommentId(commentId);
  };

  const editor = useEditor({
    immediatelyRender: false,
    content: initialContent || "",

    onCreate({ editor }) {
      console.log("on onCreate updated");
      // Set document ID in editor storage for history tracking
      editor.storage.documentId = documentId;
      setEditor(editor);
    },
    onDestroy() {
      console.log("on onDestroy updated");
      setEditor(null);
    },
    onUpdate({ editor }) {
      console.log("on onUpdate updated");
      setEditor(editor);
    },
    onSelectionUpdate({ editor }) {
      console.log("on onSelectionUpdate updated");
      setEditor(editor);
    },
    onTransaction({ editor }) {
      console.log("on onTransaction updated");
      setEditor(editor);
    },
    onFocus({ editor }) {
      console.log("on onFocus updated");
      setEditor(editor);
    },
    onBlur({ editor }) {
      console.log("on onBlur updated");
      setEditor(editor);
    },
    onContentError({ editor }) {
      console.log("on onContentError updated");
      setEditor(editor);
    },
    editorProps: {
      attributes: {
        class: "focus:outline-none bg-black",
      },
    },
    extensions: [
      StarterKit,
      TablePlus.configure({
        resizable: true,
      }),
      TableRowPlus,
      TableHeaderPlus,
      TableCellPlus,
      TaskList,
      TaskItem.configure({
        nested: true,
      }),
      TrackChangeExtension.configure({
        enabled: true,
        dataOpUserId: "", // set the op userId
        dataOpUserNickname: "", // set the op user nickname
        onStatusChange(status: boolean) {},
      }),
      Image,
      Underline,
      FontFamily,
      TextStyle,
      Color,
      LineHeightExtension.configure({
        types: ["heading", "paragraph"],
        defaultLineHeight: "1.5",
      }),
      FontSizeExtensions,
      TextAlign.configure({
        types: ["heading", "paragraph"],
      }),
      Link.configure({
        openOnClick: false,
        autolink: true,
        defaultProtocol: "https",
      }),
      Highlight.configure({
        multicolor: true,
      }),
      Placeholder.configure({
        placeholder: "Start typing...",
        emptyEditorClass: "is-editor-empty",
      }),
      PaginationPlus.configure({
        pageHeight: 1123, // A4 height in pixels at 96 DPI (297mm)
        pageGap: 10, // Gap between pages for visual separation
        pageBreakBackground: "#e2e8f0", // Darker gray background for page breaks
        pageHeaderHeight: 0,
        pageGapBorderSize: 1, // Add subtle border
        footerRight: "", // No page numbers
        footerLeft: "", // No page numbers
        headerRight: "", // No headers
        headerLeft: "", // No headers
        defaultMarginConfig: {
          top: 25.4,
          right: 25.4,
          bottom: 25.4,
          left: 25.4,
        }, // Standard A4 margins (1 inch)
        defaultPaperSize: "A4",
      }),
      CommentExtension.configure({
        HTMLAttributes: {
          class: "comment-highlight",
        },
        onCommentActivated: (commentId) => {
          console.log("Comment activated in editor:", commentId);
          setActiveCommentId(commentId);

          // If editor comments context is available, use it for navigation
          if (editorCommentsContext) {
            if (commentId) {
              console.log("Setting active comment ID in context:", commentId);
              editorCommentsContext.setActiveCommentId(commentId);
            } else {
              console.log("Clearing active comment ID in context");
              editorCommentsContext.clearActiveComment();
            }
          }

          // Only focus comment if a comment is actually clicked (commentId exists)
          if (commentId) {
            setTimeout(() => focusCommentWithActiveId(commentId), 100);
          }
        },
      }),
    ],
  });

  const handleCreateComment = useCallback(() => {
    if (!editor) return;

    const { from, to } = editor.state.selection;
    const text = editor.state.doc.textBetween(from, to);

    if (text.trim()) {
      setSelectedText(text);
      setIsCommentDialogOpen(true);
    }
  }, [editor]);

  // Expose createComment function globally so it can be called from hover menu
  useEffect(() => {
    if (typeof window !== "undefined") {
      (window as any).createComment = handleCreateComment;
    }
  }, [handleCreateComment]);

  useEffect(() => {
    if (editor && initialContent) {
      editor.commands.setContent(initialContent);
    }
  }, [editor, initialContent]);

  // Get document diff for visualization
  const documentDiff = useDocumentDiff(
    editor,
    editor?.extensionManager.extensions.find(
      (ext) => ext.name === "trackchange"
    )?.storage?.documentHistory?.lastSavedContent || ""
  );

  return (
    <div className="flex h-screen">
      {/* Main editor area */}
      <div className="flex-1 flex flex-col">
        {/* Save button and controls */}
        <div className="border-b bg-white p-3 flex items-center justify-between">
          <div className="flex items-center gap-3">
            <SaveButton />
            <button
              onClick={() => setIsHistoryPanelOpen(!isHistoryPanelOpen)}
              className="px-3 py-1 text-sm border rounded hover:bg-gray-50"
            >
              History
            </button>
            <button
              onClick={() => setShowDiffViewer(!showDiffViewer)}
              className="px-3 py-1 text-sm border rounded hover:bg-gray-50"
              disabled={!documentDiff?.hasChanges}
            >
              Show Changes
            </button>
          </div>
        </div>

        {/* Diff viewer (when enabled) */}
        {showDiffViewer && documentDiff?.hasChanges && (
          <div className="border-b">
            <DiffViewer
              originalContent={documentDiff.originalContent}
              modifiedContent={documentDiff.modifiedContent}
              onRevert={() => {
                if (editor && documentDiff.originalContent) {
                  editor.commands.setContent(documentDiff.originalContent);
                  setShowDiffViewer(false);
                }
              }}
            />
          </div>
        )}

        {/* Editor content */}
        <div ref={editorRef} className="flex-1 relative bg-slate-50">
          <div className="container mx-auto py-8 bg-[#e2e8f0] h-full">
            <EditorContent editor={editor} />
          </div>
          <AlignmentHoverMenu editorRef={editorRef} />
        </div>
      </div>

      {/* History panel (when open) */}
      {isHistoryPanelOpen && (
        <div className="w-80 border-l">
          <HistoryPanel
            isOpen={isHistoryPanelOpen}
            onToggle={() => setIsHistoryPanelOpen(!isHistoryPanelOpen)}
          />
        </div>
      )}

      {/* Comment dialog */}
      <CommentDialog
        isOpen={isCommentDialogOpen}
        onClose={() => setIsCommentDialogOpen(false)}
        onSubmit={handleCommentSubmit}
        selectedText={selectedText}
        documentId={documentId}
      />
    </div>
  );
};
