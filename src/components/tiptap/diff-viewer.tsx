import React, { use<PERSON>emo } from 'react';
import { <PERSON>roll<PERSON><PERSON> } from '@/components/ui/scroll-area';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  FileText, 
  Plus, 
  Minus, 
  Eye, 
  EyeOff,
  RotateCcw 
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface DiffChange {
  type: 'added' | 'removed' | 'unchanged';
  content: string;
  lineNumber?: number;
}

interface DiffViewerProps {
  originalContent: string;
  modifiedContent: string;
  title?: string;
  className?: string;
  showLineNumbers?: boolean;
  onRevert?: () => void;
}

// Simple diff algorithm for text comparison
const computeDiff = (original: string, modified: string): DiffChange[] => {
  const originalLines = original.split('\n');
  const modifiedLines = modified.split('\n');
  const changes: DiffChange[] = [];
  
  let i = 0, j = 0;
  
  while (i < originalLines.length || j < modifiedLines.length) {
    if (i >= originalLines.length) {
      // Only modified lines left
      changes.push({
        type: 'added',
        content: modifiedLines[j],
        lineNumber: j + 1
      });
      j++;
    } else if (j >= modifiedLines.length) {
      // Only original lines left
      changes.push({
        type: 'removed',
        content: originalLines[i],
        lineNumber: i + 1
      });
      i++;
    } else if (originalLines[i] === modifiedLines[j]) {
      // Lines are the same
      changes.push({
        type: 'unchanged',
        content: originalLines[i],
        lineNumber: j + 1
      });
      i++;
      j++;
    } else {
      // Lines are different - simple approach: mark as removed and added
      changes.push({
        type: 'removed',
        content: originalLines[i],
        lineNumber: i + 1
      });
      changes.push({
        type: 'added',
        content: modifiedLines[j],
        lineNumber: j + 1
      });
      i++;
      j++;
    }
  }
  
  return changes;
};

// Character-level diff for inline changes
const computeInlineDiff = (original: string, modified: string) => {
  if (original === modified) {
    return { type: 'unchanged', content: original };
  }
  
  // Simple character-by-character comparison
  const maxLen = Math.max(original.length, modified.length);
  const result = [];
  
  for (let i = 0; i < maxLen; i++) {
    const origChar = original[i] || '';
    const modChar = modified[i] || '';
    
    if (origChar === modChar) {
      result.push({ type: 'unchanged', char: origChar });
    } else {
      if (origChar) result.push({ type: 'removed', char: origChar });
      if (modChar) result.push({ type: 'added', char: modChar });
    }
  }
  
  return result;
};

export const DiffViewer: React.FC<DiffViewerProps> = ({
  originalContent,
  modifiedContent,
  title = "Document Changes",
  className,
  showLineNumbers = true,
  onRevert
}) => {
  const [showOnlyChanges, setShowOnlyChanges] = React.useState(false);
  
  const diff = useMemo(() => {
    return computeDiff(originalContent, modifiedContent);
  }, [originalContent, modifiedContent]);
  
  const stats = useMemo(() => {
    const added = diff.filter(change => change.type === 'added').length;
    const removed = diff.filter(change => change.type === 'removed').length;
    const unchanged = diff.filter(change => change.type === 'unchanged').length;
    
    return { added, removed, unchanged, total: diff.length };
  }, [diff]);
  
  const filteredDiff = useMemo(() => {
    if (!showOnlyChanges) return diff;
    
    // Show changes with some context
    const result = [];
    const contextLines = 2;
    
    for (let i = 0; i < diff.length; i++) {
      const change = diff[i];
      
      if (change.type !== 'unchanged') {
        // Add context before
        const startContext = Math.max(0, i - contextLines);
        for (let j = startContext; j < i; j++) {
          if (diff[j].type === 'unchanged' && !result.includes(diff[j])) {
            result.push({ ...diff[j], isContext: true });
          }
        }
        
        // Add the change
        result.push(change);
        
        // Add context after
        const endContext = Math.min(diff.length, i + contextLines + 1);
        for (let j = i + 1; j < endContext; j++) {
          if (diff[j].type === 'unchanged' && !result.includes(diff[j])) {
            result.push({ ...diff[j], isContext: true });
          }
        }
      }
    }
    
    return result;
  }, [diff, showOnlyChanges]);
  
  const renderLine = (change: DiffChange & { isContext?: boolean }, index: number) => {
    const lineClasses = cn(
      "flex items-start gap-2 px-3 py-1 text-sm font-mono",
      {
        'bg-green-50 border-l-4 border-green-400': change.type === 'added',
        'bg-red-50 border-l-4 border-red-400': change.type === 'removed',
        'bg-gray-50': change.isContext,
        'hover:bg-gray-100': change.type === 'unchanged' && !change.isContext
      }
    );
    
    const icon = change.type === 'added' ? (
      <Plus className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
    ) : change.type === 'removed' ? (
      <Minus className="h-4 w-4 text-red-600 mt-0.5 flex-shrink-0" />
    ) : null;
    
    return (
      <div key={index} className={lineClasses}>
        {showLineNumbers && (
          <span className="text-gray-400 text-xs w-8 text-right flex-shrink-0">
            {change.lineNumber}
          </span>
        )}
        <div className="flex items-start gap-2 flex-1 min-w-0">
          {icon}
          <span className={cn(
            "break-all",
            change.type === 'added' && "text-green-800",
            change.type === 'removed' && "text-red-800 line-through",
            change.isContext && "text-gray-600"
          )}>
            {change.content || '\u00A0'}
          </span>
        </div>
      </div>
    );
  };
  
  const hasChanges = stats.added > 0 || stats.removed > 0;
  
  return (
    <div className={cn("diff-viewer border rounded-lg bg-white", className)}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b bg-gray-50">
        <div className="flex items-center gap-3">
          <FileText className="h-5 w-5 text-gray-600" />
          <h3 className="font-medium">{title}</h3>
          {hasChanges && (
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="text-green-700 border-green-300">
                <Plus className="h-3 w-3 mr-1" />
                {stats.added}
              </Badge>
              <Badge variant="outline" className="text-red-700 border-red-300">
                <Minus className="h-3 w-3 mr-1" />
                {stats.removed}
              </Badge>
            </div>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowOnlyChanges(!showOnlyChanges)}
            className="text-xs"
          >
            {showOnlyChanges ? (
              <>
                <Eye className="h-3 w-3 mr-1" />
                Show All
              </>
            ) : (
              <>
                <EyeOff className="h-3 w-3 mr-1" />
                Changes Only
              </>
            )}
          </Button>
          
          {onRevert && hasChanges && (
            <Button
              variant="outline"
              size="sm"
              onClick={onRevert}
              className="text-xs"
            >
              <RotateCcw className="h-3 w-3 mr-1" />
              Revert
            </Button>
          )}
        </div>
      </div>
      
      {/* Content */}
      <div className="relative">
        {!hasChanges ? (
          <div className="text-center py-8 text-gray-500">
            <FileText className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">No changes detected</p>
            <p className="text-xs">The content is identical to the saved version</p>
          </div>
        ) : (
          <ScrollArea className="h-96">
            <div className="divide-y divide-gray-100">
              {filteredDiff.map((change, index) => renderLine(change, index))}
            </div>
          </ScrollArea>
        )}
      </div>
      
      {/* Footer */}
      {hasChanges && (
        <div className="px-4 py-2 border-t bg-gray-50 text-xs text-gray-600">
          <div className="flex items-center justify-between">
            <span>
              {stats.total} lines • {stats.added} additions • {stats.removed} deletions
            </span>
            {showOnlyChanges && (
              <span className="text-amber-600">
                Showing changes with context
              </span>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

// Hook for comparing editor content with saved content
export const useDocumentDiff = (editor: any, savedContent: string) => {
  return useMemo(() => {
    if (!editor) return null;
    
    const currentContent = editor.getText();
    return {
      originalContent: savedContent,
      modifiedContent: currentContent,
      hasChanges: savedContent !== currentContent
    };
  }, [editor, savedContent]);
};
